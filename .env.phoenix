# Phoenix Observability Configuration
# Copy this file to .env and update values as needed

# Phoenix Project Configuration
PHOENIX_PROJECT_NAME=coveredcalls-agents
PHOENIX_SESSION_NAME=trading_session

# Phoenix Endpoint Configuration
# For local Phoenix instance (default)
PHOENIX_COLLECTOR_ENDPOINT=http://localhost:6006/v1/traces

# For Phoenix Cloud (uncomment and set your values)
# PHOENIX_COLLECTOR_ENDPOINT=https://your-phoenix-instance.arize.com/v1/traces
# PHOENIX_API_KEY=your-api-key-here
# PHOENIX_CLIENT_HEADERS=api_key=your-api-key-here

# Phoenix Server Configuration (for docker-compose)
PHOENIX_HOST=0.0.0.0
PHOENIX_PORT=6006
PHOENIX_GRPC_PORT=4317
PHOENIX_HTTP_PORT=4318

# Database Configuration (for Phoenix with PostgreSQL)
PHOENIX_SQL_DATABASE_URL=postgresql://phoenix_user:phoenix_pass@localhost:5433/phoenix_db

# Phoenix Features
PHOENIX_ENABLE_PROMETHEUS=true
PHOENIX_ENABLE_CORS=true
PHOENIX_LOG_LEVEL=INFO

# Auto-instrumentation Configuration
PHOENIX_AUTO_INSTRUMENT=true
PHOENIX_BATCH_PROCESSING=true

# Tracing Configuration
PHOENIX_TRACE_SAMPLING_RATE=1.0
PHOENIX_MAX_SPAN_ATTRIBUTES=100
PHOENIX_MAX_SPAN_EVENTS=100

# Memory Integration
PHOENIX_MEMORY_CORRELATION=true
PHOENIX_STORE_TRACES_IN_MEMORY=true

# Performance Settings
PHOENIX_BATCH_TIMEOUT_MS=5000
PHOENIX_MAX_BATCH_SIZE=512
PHOENIX_EXPORT_TIMEOUT_MS=30000

# Development Settings
PHOENIX_DEBUG_MODE=false
PHOENIX_VERBOSE_LOGGING=false
