Requirement already satisfied: arize-phoenix-otel in ./venv/lib/python3.12/site-packages (0.12.1)
Collecting openinference-instrumentation-openai
  Downloading openinference_instrumentation_openai-0.1.30-py3-none-any.whl.metadata (4.7 kB)
Collecting openinference-instrumentation-langchain
  Downloading openinference_instrumentation_langchain-0.1.46-py3-none-any.whl.metadata (5.1 kB)
Requirement already satisfied: openinference-instrumentation>=0.1.34 in ./venv/lib/python3.12/site-packages (from arize-phoenix-otel) (0.1.34)
Requirement already satisfied: openinference-semantic-conventions>=0.1.17 in ./venv/lib/python3.12/site-packages (from arize-phoenix-otel) (0.1.21)
Requirement already satisfied: opentelemetry-exporter-otlp in ./venv/lib/python3.12/site-packages (from arize-phoenix-otel) (1.34.1)
Requirement already satisfied: opentelemetry-proto>=1.12.0 in ./venv/lib/python3.12/site-packages (from arize-phoenix-otel) (1.34.1)
Requirement already satisfied: opentelemetry-sdk in ./venv/lib/python3.12/site-packages (from arize-phoenix-otel) (1.34.1)
Requirement already satisfied: opentelemetry-semantic-conventions in ./venv/lib/python3.12/site-packages (from arize-phoenix-otel) (0.55b1)
Requirement already satisfied: typing-extensions<5,>=4.5 in ./venv/lib/python3.12/site-packages (from arize-phoenix-otel) (4.12.2)
Requirement already satisfied: wrapt in ./venv/lib/python3.12/site-packages (from arize-phoenix-otel) (1.17.2)
Requirement already satisfied: opentelemetry-api in ./venv/lib/python3.12/site-packages (from openinference-instrumentation-openai) (1.34.1)
Collecting opentelemetry-instrumentation (from openinference-instrumentation-openai)
  Downloading opentelemetry_instrumentation-0.56b0-py3-none-any.whl.metadata (6.7 kB)
Requirement already satisfied: protobuf<6.0,>=5.0 in ./venv/lib/python3.12/site-packages (from opentelemetry-proto>=1.12.0->arize-phoenix-otel) (5.29.5)
Requirement already satisfied: importlib-metadata<8.8.0,>=6.0 in ./venv/lib/python3.12/site-packages (from opentelemetry-api->openinference-instrumentation-openai) (8.7.0)
Requirement already satisfied: opentelemetry-exporter-otlp-proto-grpc==1.34.1 in ./venv/lib/python3.12/site-packages (from opentelemetry-exporter-otlp->arize-phoenix-otel) (1.34.1)
Requirement already satisfied: opentelemetry-exporter-otlp-proto-http==1.34.1 in ./venv/lib/python3.12/site-packages (from opentelemetry-exporter-otlp->arize-phoenix-otel) (1.34.1)
Requirement already satisfied: googleapis-common-protos~=1.52 in ./venv/lib/python3.12/site-packages (from opentelemetry-exporter-otlp-proto-grpc==1.34.1->opentelemetry-exporter-otlp->arize-phoenix-otel) (1.70.0)
Requirement already satisfied: grpcio<2.0.0,>=1.63.2 in ./venv/lib/python3.12/site-packages (from opentelemetry-exporter-otlp-proto-grpc==1.34.1->opentelemetry-exporter-otlp->arize-phoenix-otel) (1.73.1)
Requirement already satisfied: opentelemetry-exporter-otlp-proto-common==1.34.1 in ./venv/lib/python3.12/site-packages (from opentelemetry-exporter-otlp-proto-grpc==1.34.1->opentelemetry-exporter-otlp->arize-phoenix-otel) (1.34.1)
Requirement already satisfied: requests~=2.7 in ./venv/lib/python3.12/site-packages (from opentelemetry-exporter-otlp-proto-http==1.34.1->opentelemetry-exporter-otlp->arize-phoenix-otel) (2.32.3)
INFO: pip is looking at multiple versions of opentelemetry-instrumentation to determine which version is compatible with other requirements. This could take a while.
  Downloading opentelemetry_instrumentation-0.55b1-py3-none-any.whl.metadata (6.7 kB)
Requirement already satisfied: packaging>=18.0 in ./venv/lib/python3.12/site-packages (from opentelemetry-instrumentation->openinference-instrumentation-openai) (24.2)
Requirement already satisfied: zipp>=3.20 in ./venv/lib/python3.12/site-packages (from importlib-metadata<8.8.0,>=6.0->opentelemetry-api->openinference-instrumentation-openai) (3.23.0)
Requirement already satisfied: charset-normalizer<4,>=2 in ./venv/lib/python3.12/site-packages (from requests~=2.7->opentelemetry-exporter-otlp-proto-http==1.34.1->opentelemetry-exporter-otlp->arize-phoenix-otel) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in ./venv/lib/python3.12/site-packages (from requests~=2.7->opentelemetry-exporter-otlp-proto-http==1.34.1->opentelemetry-exporter-otlp->arize-phoenix-otel) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in ./venv/lib/python3.12/site-packages (from requests~=2.7->opentelemetry-exporter-otlp-proto-http==1.34.1->opentelemetry-exporter-otlp->arize-phoenix-otel) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in ./venv/lib/python3.12/site-packages (from requests~=2.7->opentelemetry-exporter-otlp-proto-http==1.34.1->opentelemetry-exporter-otlp->arize-phoenix-otel) (2025.1.31)
Downloading openinference_instrumentation_openai-0.1.30-py3-none-any.whl (28 kB)
Downloading openinference_instrumentation_langchain-0.1.46-py3-none-any.whl (19 kB)
Downloading opentelemetry_instrumentation-0.55b1-py3-none-any.whl (31 kB)
Installing collected packages: opentelemetry-instrumentation, openinference-instrumentation-openai, openinference-instrumentation-langchain
Successfully installed openinference-instrumentation-langchain-0.1.46 openinference-instrumentation-openai-0.1.30 opentelemetry-instrumentation-0.55b1
